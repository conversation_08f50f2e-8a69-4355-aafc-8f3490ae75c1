<template>
  <div class="route-schedule-container">


    <!-- 线路选择区域 -->
    <div class="route-selection-card">
      <div class="route-selector">
        <div class="selector-label">
          <el-icon><Position /></el-icon>
          选择公交线路：
        </div>
        <el-select
          v-model="selectedRouteId"
          placeholder="请选择要排班的线路"
          size="large"
          style="width: 300px"
          @change="handleRouteChange"
          :loading="routesLoading"
        >
          <el-option
            v-for="route in routes"
            :key="route.routeId"
            :label="`${route.routeName} (${route.routeCode})`"
            :value="route.routeId"
          />
        </el-select>
      </div>

      <div v-if="selectedRoute" class="route-info">
        <div class="route-details">
          <div class="route-name">{{ selectedRoute.routeName }}</div>
          <div class="route-meta">
            <span class="route-code">{{ selectedRoute.routeCode }}</span>
            <span class="route-distance">{{ selectedRoute.distance }}km</span>
            <span class="route-stations">{{ selectedRoute.stationCount }}站</span>
          </div>
          <div class="route-description">{{ selectedRoute.description }}</div>
        </div>
        <div class="route-stats">
          <el-statistic title="绑定车辆" :value="routeVehicles.length" suffix="辆" />
          <el-statistic title="发车时间点" :value="routeTimePoints.length" suffix="个" />
          <el-statistic title="已分配" :value="assignedCount" suffix="班次" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="selectedRoute" class="main-content">
      <!-- 线路车辆展示区 -->
      <div class="vehicles-section">
        <div class="section-header">
          <h3><el-icon><Van /></el-icon>线路车辆 ({{ routeVehicles.length }}辆)</h3>
          <div class="vehicle-filters">
            <el-radio-group v-model="vehicleFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="available">可用</el-radio-button>
              <el-radio-button label="assigned">已分配</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="vehicles-grid">
          <div
            v-for="vehicle in filteredVehicles"
            :key="vehicle.vehicleId"
            class="vehicle-item"
            :class="{
              'assigned': vehicle.assignedTimes && vehicle.assignedTimes.length > 0,
              'unavailable': vehicle.status !== '1'
            }"
            draggable="true"
            @dragstart="handleVehicleDragStart(vehicle, $event)"
            @dragend="handleVehicleDragEnd"
          >
            <div class="vehicle-icon">
              <el-icon><Van /></el-icon>
            </div>
            <div class="vehicle-info">
              <div class="plate-number">{{ vehicle.plateNumber }}</div>
              <div v-if="vehicle.assignedTimes && vehicle.assignedTimes.length > 0" class="assignment-info">
                {{ vehicle.assignedTimes.length }}趟
              </div>
            </div>


          </div>
        </div>

        <div v-if="filteredVehicles.length === 0" class="empty-vehicles">
          <el-empty description="暂无符合条件的车辆" />
        </div>
      </div>

      <!-- 发车时间配置区 -->
      <div class="schedule-section">
        <div class="section-header">
          <h3><el-icon><Clock /></el-icon>发车时间配置</h3>
          <div class="schedule-actions">
            <el-dropdown @command="applyTimeTemplate">
              <el-button size="small" type="success">
                快速配置<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="peak">高峰期模式</el-dropdown-item>
                  <el-dropdown-item command="normal">平峰期模式</el-dropdown-item>
                  <el-dropdown-item command="weekend">周末模式</el-dropdown-item>
                  <el-dropdown-item command="holiday">节假日模式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

          </div>
        </div>

        <!-- 简单SVG时间线 -->
        <div class="timeline-visual">
          <div class="timeline-container">
            <!-- SVG连接线 -->
            <svg class="timeline-connections" v-if="routeTimePoints.length > 1">
              <path 
                :d="generateSimpleConnectionPath()" 
                stroke="#409eff" 
                stroke-width="3" 
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            
            <!-- 时间节点网格 -->
            <div class="timeline-track">
              <div
                v-for="(timePoint, index) in routeTimePoints"
                :key="`time-${index}`"
                class="timeline-node"
                :class="{
                  'highlight': highlightedTimeIndex === index,
                  'peak-time': timePoint.type === 'peak',
                  'has-vehicle': getAssignedVehicle(timePoint.time)
                }"
                :style="getNodeStyle(index)"
                @drop="handleTimePointDrop($event, timePoint.time)"
                @dragover="handleDragOver"
                @dragenter="handleDragEnter($event, index)"
                @dragleave="handleDragLeave"
              >
                <div class="time-info">
                  <div class="time-text">{{ timePoint.time }}</div>
                  <div class="time-sequence">{{ index + 1 }}</div>
                </div>
                
                <div class="vehicle-display">
                  <div v-if="getAssignedVehicle(timePoint.time)" class="assigned-vehicle-compact">
                    <div class="vehicle-icon-compact">
                      <el-icon><Van /></el-icon>
                    </div>
                    <div class="vehicle-plate-compact">{{ getAssignedVehicle(timePoint.time)?.plateNumber }}</div>
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      @click="removeAssignment(timePoint.time)"
                      class="remove-btn-compact"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div v-else class="empty-slot">
                    <div class="empty-icon">
                      <el-icon><Plus /></el-icon>
                    </div>
                    <div class="empty-text">拖拽分配</div>
                  </div>
                </div>
                
                <div class="quick-actions">
                  <el-button
                    v-if="!getAssignedVehicle(timePoint.time)"
                    @click="autoAssignSingle(timePoint.time)"
                    size="small"
                    type="primary"
                    plain
                    class="auto-assign-btn"
                  >
                    自动
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="routeTimePoints.length === 0" class="empty-schedule">
          <el-empty description="暂无发车时间配置" />
        </div>

        <!-- 操作按钮区域 -->
        <div v-if="routeTimePoints.length > 0" class="action-buttons">
          <el-button-group>
            <el-button type="success" @click="autoAssignAll" :loading="autoAssigning" size="large">
              <el-icon><MagicStick /></el-icon>
              一键排班
            </el-button>
            <el-button type="primary" @click="saveSchedule" :loading="saving" size="large">
              <el-icon><Check /></el-icon>
              保存计划
            </el-button>
            <el-button @click="clearAllAssignments" size="large">
              <el-icon><RefreshLeft /></el-icon>
              清空分配
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 未选择线路时的提示 -->
    <div v-else class="no-route-selected">
      <el-result
        icon="info"
        title="请选择公交线路"
        sub-title="选择线路后将显示该线路的车辆和发车时间配置"
      >
        <template #extra>
          <el-button type="primary" @click="focusRouteSelector">
            选择线路
          </el-button>
        </template>
      </el-result>
    </div>



    <!-- 拖拽提示层 -->
    <div v-if="isDragging" class="drag-overlay">
      <div class="drag-hint">
        <el-icon><Rank /></el-icon>
        <span>拖拽到发车时间点进行分配</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Share, MagicStick, Check, RefreshLeft, Position, Van, Clock,
  ArrowDown, Setting, Plus, Close, MoreFilled, Rank
} from '@element-plus/icons-vue'

// 接口定义
interface BusRoute {
  routeId: number | string
  routeName: string
  routeCode: string
  distance: number
  stationCount: number
  description: string
  vehicleIds: (number | string)[]
  timePoints: TimePoint[]
}

interface Vehicle {
  vehicleId: number | string
  plateNumber: string
  vehicleNumber: string
  vehicleType: 'bus' | 'electric_bus' | 'hybrid_bus' | 'minibus'
  seatCount: number
  status: '1' | '0' | '2' | '3'
  routeId?: number | string
  assignedTimes?: string[] // 改为数组，记录所有分配的时间点
}

interface TimePoint {
  time: string
  type?: 'peak' | 'normal' | 'weekend' | 'holiday'
  frequency?: number
}

interface Assignment {
  timePoint: string
  vehicleId: number | string
  plateNumber: string
}

// 响应式数据
const routes = ref<BusRoute[]>([])
const vehicles = ref<Vehicle[]>([])
const selectedRouteId = ref<number | string | null>(null)
const assignments = ref<Assignment[]>([])
const routesLoading = ref(false)
const autoAssigning = ref(false)
const saving = ref(false)
const isDragging = ref(false)
const highlightedTimeIndex = ref(-1)
const vehicleFilter = ref('all')

// 简单的蛇形排列计算
const getSnakePosition = (index: number) => {
  const cols = 6
  const row = Math.floor(index / cols)
  const col = index % cols
  const isReverseRow = row % 2 === 1

  return {
    row,
    col: isReverseRow ? cols - 1 - col : col,
    isReverse: isReverseRow
  }
}

// 节点CSS Grid定位
const getNodeStyle = (index: number) => {
  const pos = getSnakePosition(index)
  return {
    gridRow: pos.row + 1,
    gridColumn: pos.col + 1
  }
}

// 生成简单清晰的连接路径
const generateSimpleConnectionPath = () => {
  if (routeTimePoints.value.length < 2) return ''
  
  const cols = 6
  const nodeWidth = 160 // 节点实际宽度
  const nodeHeight = 120 // 节点实际高度
  const gapX = 20 // 水平间距
  const gapY = 20 // 垂直间距
  
  // 精确计算节点中心位置 - 考虑padding和实际布局
  const containerPadding = 20 // 容器内边距
  const nodeCenterX = nodeWidth / 2 // 节点中心X偏移
  const nodeCenterY = nodeHeight / 2 // 节点中心Y偏移
  
  let path = ''
  
  for (let i = 0; i < routeTimePoints.value.length; i++) {
    const pos = getSnakePosition(i)
    // 精确计算节点中心位置
    const x = containerPadding + pos.col * (nodeWidth + gapX) + nodeCenterX
    const y = containerPadding + pos.row * (nodeHeight + gapY) + nodeCenterY
    
    if (i === 0) {
      path += `M ${x} ${y}`
    } else {
      const prevPos = getSnakePosition(i - 1)
      const prevX = containerPadding + prevPos.col * (nodeWidth + gapX) + nodeCenterX
      const prevY = containerPadding + prevPos.row * (nodeHeight + gapY) + nodeCenterY
      
      if (pos.row === prevPos.row) {
        // 同一行，直线连接
        path += ` L ${x} ${y}`
      } else {
        // 换行，L型连接：先向下，再水平
        path += ` L ${prevX} ${y} L ${x} ${y}`
      }
    }
  }
  
  return path
}

// 计算属性
const selectedRoute = computed(() => {
  return routes.value.find(r => r.routeId === selectedRouteId.value) || null
})

const routeVehicles = computed(() => {
  if (!selectedRoute.value) return []
  return vehicles.value.filter(v =>
    selectedRoute.value?.vehicleIds.includes(v.vehicleId)
  )
})

const routeTimePoints = computed(() => {
  return selectedRoute.value?.timePoints || []
})

const filteredVehicles = computed(() => {
  let filtered = routeVehicles.value

  switch (vehicleFilter.value) {
    case 'available':
      filtered = filtered.filter(v => v.status === '1')
      break
    case 'assigned':
      filtered = filtered.filter(v => v.assignedTimes && v.assignedTimes.length > 0)
      break
  }

  return filtered
})

const assignedCount = computed(() => {
  return assignments.value.length
})

// 辅助函数：时间转换为分钟数
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

// 事件处理
const handleRouteChange = (routeId: number | string) => {
  selectedRouteId.value = routeId
  // 清空之前的分配
  clearAllAssignments()
  ElMessage.success(`已选择线路：${selectedRoute.value?.routeName}`)
}

const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  if (vehicle.status !== '1') {
    event.preventDefault()
    ElMessage.warning('该车辆状态不可用')
    return
  }

  event.dataTransfer!.setData('text/plain', JSON.stringify({
    type: 'vehicle',
    vehicleId: vehicle.vehicleId,
    plateNumber: vehicle.plateNumber
  }))

  isDragging.value = true
}

const handleVehicleDragEnd = () => {
  isDragging.value = false
  highlightedTimeIndex.value = -1
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent, index: number) => {
  event.preventDefault()
  highlightedTimeIndex.value = index
}

const handleDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了拖拽区域
  if (!event.currentTarget || !(event.currentTarget as Element).contains(event.relatedTarget as Node)) {
    highlightedTimeIndex.value = -1
  }
}

const handleTimePointDrop = (event: DragEvent, timePoint: string) => {
  event.preventDefault()
  highlightedTimeIndex.value = -1
  isDragging.value = false

  try {
    const dragData = JSON.parse(event.dataTransfer!.getData('text/plain'))

    if (dragData.type === 'vehicle') {
      assignVehicleToTimePoint(dragData.vehicleId, dragData.plateNumber, timePoint)
    }
  } catch (error) {
    ElMessage.error('分配失败')
  }
}

// 辅助函数：检查车辆在指定时间是否可用（考虑行程时间）
const isVehicleAvailableAtTime = (vehicleId: number | string, targetTime: string): boolean => {
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  if (!vehicle || !vehicle.assignedTimes || vehicle.assignedTimes.length === 0) {
    return true
  }

  const targetMinutes = timeToMinutes(targetTime)
  const tripDuration = 90 // 假设一趟车需要90分钟（包括行程和休息时间）

  // 检查是否与已分配的时间冲突
  for (const assignedTime of vehicle.assignedTimes) {
    const assignedMinutes = timeToMinutes(assignedTime)
    const timeDiff = Math.abs(targetMinutes - assignedMinutes)

    if (timeDiff < tripDuration) {
      return false // 时间冲突
    }
  }

  return true
}

// 辅助函数：获取车辆的分配次数
const getVehicleAssignmentCount = (vehicleId: number | string): number => {
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  return vehicle?.assignedTimes?.length || 0
}

// 工具函数

const getVehicleTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    bus: '普通公交',
    electric_bus: '电动公交',
    hybrid_bus: '混动公交',
    minibus: '小型公交'
  }
  return typeMap[type] || type
}

const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',
    '0': 'info',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '0': '停用',
    '2': '维修中',
    '3': '故障'
  }
  return statusMap[status] || '未知'
}

const getAssignedVehicle = (timePoint: string): Vehicle | null => {
  const assignment = assignments.value.find(a => a.timePoint === timePoint)
  if (!assignment) return null
  return vehicles.value.find(v => v.vehicleId === assignment.vehicleId) || null
}

const assignVehicleToTimePoint = (vehicleId: number | string, plateNumber: string, timePoint: string) => {
  // 检查该时间点是否已有分配
  const existingAssignment = assignments.value.find(a => a.timePoint === timePoint)
  if (existingAssignment) {
    ElMessage.warning(`时间点 ${timePoint} 已有车辆分配`)
    return
  }

  // 检查车辆在该时间是否可用（时间冲突检测）
  if (!isVehicleAvailableAtTime(vehicleId, timePoint)) {
    ElMessage.warning(`车辆 ${plateNumber} 在 ${timePoint} 时间冲突，请选择其他时间`)
    return
  }

  // 创建新分配
  assignments.value.push({
    timePoint,
    vehicleId,
    plateNumber
  })

  // 更新车辆的分配时间数组
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  if (vehicle) {
    if (!vehicle.assignedTimes) {
      vehicle.assignedTimes = []
    }
    vehicle.assignedTimes.push(timePoint)
    // 按时间排序
    vehicle.assignedTimes.sort()
  }

  const assignmentCount = getVehicleAssignmentCount(vehicleId)
  ElMessage.success(`车辆 ${plateNumber} 已分配到 ${timePoint}（第${assignmentCount}趟）`)
}

const removeAssignment = (timePoint: string) => {
  const assignmentIndex = assignments.value.findIndex(a => a.timePoint === timePoint)
  if (assignmentIndex === -1) return

  const assignment = assignments.value[assignmentIndex]

  // 移除分配
  assignments.value.splice(assignmentIndex, 1)

  // 从车辆的分配时间数组中移除该时间点
  const vehicle = vehicles.value.find(v => v.vehicleId === assignment.vehicleId)
  if (vehicle && vehicle.assignedTimes) {
    const timeIndex = vehicle.assignedTimes.indexOf(timePoint)
    if (timeIndex > -1) {
      vehicle.assignedTimes.splice(timeIndex, 1)
    }
  }

  ElMessage.success(`已取消 ${timePoint} 的车辆分配`)
}

const handleTimePointAction = (command: string, timePoint: string) => {
  switch (command) {
    case 'auto-assign':
      autoAssignSingle(timePoint)
      break
    case 'clear':
      removeAssignment(timePoint)
      break
    case 'edit':
      // 编辑单个时间点
      ElMessage.info('编辑功能开发中')
      break
  }
}

const autoAssignSingle = (timePoint: string) => {
  // 获取在该时间点可用的车辆（按分配次数排序，优先选择分配次数少的）
  const availableVehicles = routeVehicles.value
    .filter(v => v.status === '1' && isVehicleAvailableAtTime(v.vehicleId, timePoint))
    .sort((a, b) => getVehicleAssignmentCount(a.vehicleId) - getVehicleAssignmentCount(b.vehicleId))

  if (availableVehicles.length === 0) {
    ElMessage.warning('该时间点没有可用的车辆')
    return
  }

  // 选择分配次数最少的车辆
  const vehicle = availableVehicles[0]
  assignVehicleToTimePoint(vehicle.vehicleId, vehicle.plateNumber, timePoint)
}

const autoAssignAll = async () => {
  if (!selectedRoute.value) return

  const unassignedTimePoints = routeTimePoints.value.filter(tp =>
    !assignments.value.some(a => a.timePoint === tp.time)
  )

  if (unassignedTimePoints.length === 0) {
    ElMessage.warning('所有时间点都已分配')
    return
  }

  autoAssigning.value = true
  let successCount = 0

  try {
    // 模拟智能分配过程
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 为每个未分配的时间点寻找最佳车辆
    for (const tp of unassignedTimePoints) {
      const availableVehicles = routeVehicles.value
        .filter(v => v.status === '1' && isVehicleAvailableAtTime(v.vehicleId, tp.time))
        .sort((a, b) => getVehicleAssignmentCount(a.vehicleId) - getVehicleAssignmentCount(b.vehicleId))

      if (availableVehicles.length > 0) {
        const vehicle = availableVehicles[0]
        assignVehicleToTimePoint(vehicle.vehicleId, vehicle.plateNumber, tp.time)
        successCount++
      }
    }

    if (successCount > 0) {
      ElMessage.success(`一键排班完成！共分配 ${successCount} 个班次`)
    } else {
      ElMessage.warning('没有找到合适的车辆分配方案')
    }
  } finally {
    autoAssigning.value = false
  }
}

const clearAllAssignments = () => {
  assignments.value = []
  vehicles.value.forEach(v => {
    v.assignedTimes = []
  })
  ElMessage.success('已清空所有分配')
}

const saveSchedule = async () => {
  if (assignments.value.length === 0) {
    ElMessage.warning('请先进行排班分配')
    return
  }

  saving.value = true
  try {
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('排班计划保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const applyTimeTemplate = (template: string) => {
  let templateTimes: TimePoint[] = []

  switch (template) {
    case 'peak':
      templateTimes = [
        { time: '06:00', type: 'peak' },
        { time: '06:15', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '06:45', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:15', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '07:45', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:15', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '17:45', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:15', type: 'peak' },
        { time: '18:30', type: 'peak' }
      ]
      break
    case 'normal':
      templateTimes = [
        { time: '06:00', type: 'normal' },
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'normal' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '12:00', type: 'normal' },
        { time: '14:00', type: 'normal' },
        { time: '15:00', type: 'normal' },
        { time: '16:00', type: 'normal' },
        { time: '17:00', type: 'normal' },
        { time: '18:00', type: 'normal' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ]
      break
    case 'weekend':
      templateTimes = [
        { time: '07:00', type: 'weekend' },
        { time: '08:00', type: 'weekend' },
        { time: '09:00', type: 'weekend' },
        { time: '10:00', type: 'weekend' },
        { time: '11:00', type: 'weekend' },
        { time: '14:00', type: 'weekend' },
        { time: '15:00', type: 'weekend' },
        { time: '16:00', type: 'weekend' },
        { time: '17:00', type: 'weekend' },
        { time: '18:00', type: 'weekend' },
        { time: '19:00', type: 'weekend' },
        { time: '20:00', type: 'weekend' },
        { time: '21:00', type: 'weekend' }
      ]
      break
    case 'holiday':
      templateTimes = [
        { time: '08:00', type: 'holiday' },
        { time: '09:00', type: 'holiday' },
        { time: '10:00', type: 'holiday' },
        { time: '11:00', type: 'holiday' },
        { time: '14:00', type: 'holiday' },
        { time: '15:00', type: 'holiday' },
        { time: '16:00', type: 'holiday' },
        { time: '17:00', type: 'holiday' },
        { time: '18:00', type: 'holiday' },
        { time: '19:00', type: 'holiday' },
        { time: '20:00', type: 'holiday' }
      ]
      break
  }

  if (selectedRoute.value) {
    selectedRoute.value.timePoints = templateTimes
    clearAllAssignments()
    ElMessage.success(`已应用${template}模式的发车时间配置`)
  }
}



const focusRouteSelector = () => {
  // 聚焦到线路选择器
}

// 初始化数据
const initializeData = () => {
  // 模拟线路数据
  routes.value = [
    {
      routeId: 1,
      routeName: '1路公交',
      routeCode: 'BUS-001',
      distance: 15.2,
      stationCount: 28,
      description: '火车站 - 市政府 - 商业中心',
      vehicleIds: [1, 2, 3, 4],
      timePoints: [
        { time: '06:00', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:30', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ]
    },
    {
      routeId: 2,
      routeName: '2路公交',
      routeCode: 'BUS-002',
      distance: 12.8,
      stationCount: 22,
      description: '体育场 - 大学城 - 科技园',
      vehicleIds: [5, 6, 7],
      timePoints: [
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:30', type: 'normal' }
      ]
    },
    {
      routeId: 3,
      routeName: '3路公交',
      routeCode: 'BUS-003',
      distance: 18.5,
      stationCount: 35,
      description: '北站 - 市中心 - 南站',
      vehicleIds: [8, 9, 10, 11, 12],
      timePoints: [
        { time: '05:30', type: 'normal' },
        { time: '06:00', type: 'peak' },
        { time: '06:20', type: 'peak' },
        { time: '06:40', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:20', type: 'peak' },
        { time: '07:40', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '08:30', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '09:30', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '16:30', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:20', type: 'peak' },
        { time: '17:40', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:20', type: 'peak' },
        { time: '18:40', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:30', type: 'normal' }
      ]
    }
  ]

  // 模拟车辆数据
  vehicles.value = [
    { vehicleId: 1, plateNumber: '京A12345', vehicleNumber: 'V001', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 1 },
    { vehicleId: 2, plateNumber: '京A12346', vehicleNumber: 'V002', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 1 },
    { vehicleId: 3, plateNumber: '京A12347', vehicleNumber: 'V003', vehicleType: 'bus', seatCount: 40, status: '1', routeId: 1 },
    { vehicleId: 4, plateNumber: '京A12348', vehicleNumber: 'V004', vehicleType: 'hybrid_bus', seatCount: 32, status: '2', routeId: 1 },
    { vehicleId: 5, plateNumber: '京A12349', vehicleNumber: 'V005', vehicleType: 'electric_bus', seatCount: 28, status: '1', routeId: 2 },
    { vehicleId: 6, plateNumber: '京A12350', vehicleNumber: 'V006', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 2 },
    { vehicleId: 7, plateNumber: '京A12351', vehicleNumber: 'V007', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 2 },
    { vehicleId: 8, plateNumber: '京A12352', vehicleNumber: 'V008', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 9, plateNumber: '京A12353', vehicleNumber: 'V009', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 10, plateNumber: '京A12354', vehicleNumber: 'V010', vehicleType: 'electric_bus', seatCount: 35, status: '1', routeId: 3 },
    { vehicleId: 11, plateNumber: '京A12355', vehicleNumber: 'V011', vehicleType: 'hybrid_bus', seatCount: 38, status: '1', routeId: 3 },
    { vehicleId: 12, plateNumber: '京A12356', vehicleNumber: 'V012', vehicleType: 'bus', seatCount: 45, status: '3', routeId: 3 }
  ]
}

// 生命周期
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.route-schedule-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 8px;
}

/* 简单SVG时间线样式 */
.timeline-visual {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-container {
  width: 100%;
  position: relative;
  min-height: 400px;
}

/* SVG连接线样式 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.timeline-track {
  display: grid;
  grid-template-columns: repeat(6, 160px);
  grid-auto-rows: 120px;
  gap: 20px;
  position: relative;
  z-index: 1;
  justify-content: center;
  padding: 20px;
}

.timeline-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 140px;
  padding: 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.timeline-node.highlight {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

.timeline-node.peak-time {
  border-color: #f56c6c;
  background: rgba(245, 108, 108, 0.15);
}

.timeline-node.has-vehicle {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
  position: relative;
}

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 3;
}

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  margin: 6px 0;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}



.action-buttons {
  margin-top: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.route-selection-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.route-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  min-width: 140px;
}

.route-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.route-details .route-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.route-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.route-description {
  font-size: 14px;
  opacity: 0.8;
}

.route-stats {
  display: flex;
  gap: 32px;
}

.route-stats :deep(.el-statistic__content) {
  color: white;
}

.route-stats :deep(.el-statistic__head) {
  color: rgba(255, 255, 255, 0.8);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.vehicles-section,
.schedule-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.vehicle-filters {
  display: flex;
  gap: 12px;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 2px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.vehicle-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.vehicle-item.assigned {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff, #ecf5ff);
}

.vehicle-item.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.vehicle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-info {
  flex: 1;
}

.plate-number {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.seat-info {
  font-size: 12px;
  color: #606266;
}

.assignment-info {
  font-size: 10px;
  color: #67c23a;
  font-weight: 600;
  margin-top: 1px;
}

.assigned-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #67c23a;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-grid {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.timeline-header {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.timeline-header > div {
  padding: 16px;
  border-right: 1px solid #e4e7ed;
}

.timeline-header > div:last-child {
  border-right: none;
}

.timeline-row {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  border-top: 1px solid #e4e7ed;
  transition: background-color 0.3s ease;
}

.timeline-row.highlight {
  background: rgba(64, 158, 255, 0.1);
}

.timeline-row > div {
  padding: 16px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.timeline-row > div:last-child {
  border-right: none;
  justify-content: center;
}

.time-cell {
  flex-direction: column;
  align-items: flex-start;
}

.time-display {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.time-type {
  font-size: 12px;
  color: #909399;
}

.assignment-cell {
  min-height: 60px;
}

.assigned-vehicle .vehicle-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border-radius: 20px;
  position: relative;
}

.vehicle-icon-small {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicle-details .plate {
  font-weight: 600;
  font-size: 12px;
}

.vehicle-details .type {
  font-size: 10px;
  opacity: 0.8;
}

.remove-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  min-height: 16px;
}

.drop-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  color: #c0c4cc;
  font-size: 12px;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 60px;
}

.timeline-row.highlight .drop-zone {
  border-color: #409eff;
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.empty-vehicles,
.empty-schedule {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.no-route-selected {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.time-config-content {
  padding: 16px;
}

.config-header {
  margin-bottom: 24px;
}

.time-preview {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.time-tag {
  margin: 2px 4px 2px 0;
}

.preview-empty {
  text-align: center;
  color: #c0c4cc;
  font-size: 14px;
  padding: 20px;
}

.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
}

.drag-hint {
  background: #409eff;
  color: white;
  padding: 16px 24px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .vehicles-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

/* 图形化时间线样式 */
.timeline-visual {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移除布局控制器样式 */

.timeline-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding: 0; /* 移除额外的padding，让SVG和Grid对齐 */
  position: relative;
  min-height: 200px;
}

/* SVG连接线样式 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0; /* 确保连接线在最底层 */
}

/* 移除时间轴背景线 */

.timeline-track {
  display: grid;
  grid-template-columns: repeat(6, 160px);
  grid-auto-rows: 120px;
  gap: 20px;
  position: relative;
  z-index: 1; /* 节点层在连接线之上 */
  justify-content: center;
  padding: 20px;
}

/* 移除蛇形布局样式 */

.timeline-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-width: 140px;
  max-width: 160px;
  padding: 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9); /* 修改为半透明背景 */
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.timeline-node.highlight {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

.timeline-node.peak-time {
  border-color: #f56c6c;
  background: rgba(245, 108, 108, 0.15); /* 降低透明度，让连接线可见 */
}

.timeline-node.has-vehicle {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.08); /* 增加透明度，保持可见性 */
}

/* 移除蛇形节点样式 */

/* 时间车辆显示 */
.time-vehicle-display {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 移除蛇形时间信息样式 */

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

/* 移除蛇形时间文本样式 */

/* 移除原时间序号样式，使用新的绝对定位样式 */

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}

/* 时间线节点圆点 */
.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2; /* 圆点在最顶层 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

/* 移除蛇形圆点样式 */

/* 移除旧的连接器样式 */

/* 移除复杂的CSS伪元素连接线系统，使用SVG替代 */

.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

.timeline-node:hover .timeline-dot {
  transform: translate(-50%, -50%) scale(1.2);
}

/* 优化时间序号显示 */
.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 3; /* 序号在所有元素之上 */
}



@media (max-width: 768px) {
  .route-schedule-container {
    padding: 4px;
  }

  .route-selector {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .selector-label {
    min-width: auto;
  }

  .route-info {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .route-stats {
    justify-content: space-around;
  }

  .vehicles-grid {
    grid-template-columns: 1fr;
  }

  .timeline-grid {
    overflow-x: auto;
  }

  .timeline-header,
  .timeline-row {
    min-width: 500px;
  }

  .timeline-track {
    justify-content: center;
    gap: 12px;
    grid-template-columns: repeat(4, 140px);
  }

  .timeline-node {
    min-width: 120px;
    max-width: 140px;
    padding: 6px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
  }

  /* 移动端网格优化 */
  .timeline-track {
    grid-template-columns: repeat(4, 140px);
    gap: 12px;
  }

  .timeline-node {
    min-width: 120px;
    max-width: 140px;
    padding: 6px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
  }
  
  .time-sequence {
    width: 18px;
    height: 18px;
    font-size: 9px;
    top: -6px;
    right: -6px;
  }
}
</style>
